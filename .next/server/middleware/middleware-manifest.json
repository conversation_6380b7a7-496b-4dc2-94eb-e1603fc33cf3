{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_e76d35f6._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4c46a49c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JZiPvQ5XzRHs0H9v2y0AQPgZIzl0VWNXe/d02EC1/6o=", "__NEXT_PREVIEW_MODE_ID": "7ca44165eb72ff824ba6e2aa68f08c47", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "041001a81408cd6f30f549119836b4dec9a1f60a6f31428729af0207f5184390", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e14c59db459c9399bd476659d35189b3112d14f09129057ba719cd2bb583071a"}}}, "instrumentation": null, "functions": {}}