{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_e76d35f6._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4c46a49c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JZiPvQ5XzRHs0H9v2y0AQPgZIzl0VWNXe/d02EC1/6o=", "__NEXT_PREVIEW_MODE_ID": "6ac4a30761872908c015614e3f2ec39e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0392bfd661c523e4d2161260d53c45d0f0bd2fa1ec03ed5cd1c855815d176278", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d90ea3709c23c7427362e8f256f42c32fd06e8d69d81f86c9e632346439c4695"}}}, "instrumentation": null, "functions": {}}