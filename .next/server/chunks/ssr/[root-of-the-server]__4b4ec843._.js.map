{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/lib/supabase.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n\n// Check if Supabase is configured\nconst isSupabaseConfigured = supabaseUrl && supabaseAnonKey &&\n  supabaseUrl !== 'your_supabase_project_url' &&\n  supabaseAnonKey !== 'your_supabase_anon_key'\n\n// Client-side Supabase client\nexport const supabase = isSupabaseConfigured\n  ? createBrowserClient(supabaseUrl!, supabaseAnonKey!)\n  : null\n\n// Database types\nexport interface User {\n  id: string\n  email: string\n  display_name: string\n  avatar_url?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface Game {\n  id: string\n  white_player_id: string\n  black_player_id?: string\n  game_state: string // FEN notation\n  moves: string[] // Array of moves in algebraic notation\n  status: 'waiting' | 'active' | 'completed' | 'abandoned'\n  winner?: 'white' | 'black' | 'draw'\n  created_at: string\n  updated_at: string\n}\n\nexport interface GameMove {\n  id: string\n  game_id: string\n  player_id: string\n  move: string\n  fen_after: string\n  created_at: string\n}\n\nexport interface ChatMessage {\n  id: string\n  game_id: string\n  user_id: string\n  message: string\n  created_at: string\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEA,MAAM;AACN,MAAM;AAEN,kCAAkC;AAClC,MAAM,uBAAuB,eAAe,mBAC1C,gBAAgB,+BAChB,oBAAoB;AAGf,MAAM,WAAW,uCACpB,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAc", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User as SupabaseUser } from '@supabase/supabase-js'\nimport { supabase, User } from '@/lib/supabase'\n\ninterface AuthContextType {\n  user: User | null\n  supabaseUser: SupabaseUser | null\n  loading: boolean\n  signInWithGoogle: () => Promise<void>\n  signOut: () => Promise<void>\n  updateUserProfile: (updates: Partial<Pick<User, 'display_name' | 'avatar_url'>>) => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [supabaseUser, setSupabaseUser] = useState<SupabaseUser | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Check if Supabase is configured\n    if (!supabase) {\n      setLoading(false)\n      return\n    }\n\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session } } = await supabase!.auth.getSession()\n      if (session?.user) {\n        setSupabaseUser(session.user)\n        try {\n          await fetchUserProfile(session.user.id)\n        } catch (error) {\n          console.error('Failed to fetch initial user profile, using fallback:', error)\n          createFallbackUser(session.user)\n        }\n      }\n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase!.auth.onAuthStateChange(\n      async (event, session) => {\n        console.log('Auth state change:', event, session?.user?.email)\n\n        if (session?.user) {\n          setSupabaseUser(session.user)\n\n          // Try to fetch user profile, but don't block on it\n          try {\n            await fetchUserProfile(session.user.id)\n          } catch (error) {\n            console.error('Failed to fetch user profile, using fallback:', error)\n            // Fallback: create user object from auth data\n            createFallbackUser(session.user)\n          }\n        } else {\n          setSupabaseUser(null)\n          setUser(null)\n        }\n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const createFallbackUser = (authUser: SupabaseUser) => {\n    console.log('Creating fallback user from auth data:', authUser.email)\n\n    const displayName = authUser.user_metadata?.display_name ||\n                       authUser.user_metadata?.name ||\n                       authUser.user_metadata?.full_name ||\n                       authUser.user_metadata?.given_name ||\n                       authUser.email?.split('@')[0] ||\n                       'Anonymous'\n\n    const fallbackUser: User = {\n      id: authUser.id,\n      email: authUser.email!,\n      display_name: displayName,\n      avatar_url: authUser.user_metadata?.avatar_url || authUser.user_metadata?.picture,\n      created_at: authUser.created_at,\n      updated_at: new Date().toISOString()\n    }\n\n    setUser(fallbackUser)\n    console.log('Fallback user created:', fallbackUser)\n  }\n\n  const fetchUserProfile = async (userId: string) => {\n    if (!supabase) return\n\n    try {\n      // First, let's try to get the current session to ensure we have proper auth\n      const { data: sessionData } = await supabase.auth.getSession()\n      if (!sessionData.session) {\n        console.log('No active session, skipping user profile fetch')\n        return\n      }\n\n      const { data, error } = await supabase\n        .from('users')\n        .select('*')\n        .eq('id', userId)\n        .maybeSingle() // Use maybeSingle instead of single to avoid errors when no rows\n\n      if (error) {\n        console.error('Error fetching user profile:', {\n          message: error.message,\n          details: error.details,\n          hint: error.hint,\n          code: error.code,\n          userId: userId\n        })\n\n        // Handle specific error cases\n        if (error.code === 'PGRST116' || error.message.includes('406') || !data) {\n          console.log('User not found in users table, attempting to create...')\n          await createUserProfile(userId)\n          return\n        }\n\n        // For other errors, we'll continue without setting user data\n        // but won't block the authentication flow\n        return\n      }\n\n      if (data) {\n        setUser(data)\n      } else {\n        // No user found, try to create one\n        console.log('No user data returned, attempting to create user profile...')\n        await createUserProfile(userId)\n      }\n    } catch (error) {\n      console.error('Error fetching user profile:', error)\n      // Don't block auth flow, try to create user profile as fallback\n      await createUserProfile(userId)\n    }\n  }\n\n  const createUserProfile = async (userId: string) => {\n    if (!supabase || !supabaseUser) return\n\n    try {\n      // Get user info from Supabase auth\n      const { data: authUser, error: authError } = await supabase.auth.getUser()\n\n      if (authError) {\n        console.error('Error getting auth user:', authError)\n        return\n      }\n\n      const user = authUser.user\n      if (!user) return\n\n      // Extract display name and avatar from user metadata\n      const displayName = user.user_metadata?.display_name ||\n                         user.user_metadata?.name ||\n                         user.user_metadata?.full_name ||\n                         user.user_metadata?.given_name ||\n                         user.email?.split('@')[0] ||\n                         'Anonymous'\n\n      const avatarUrl = user.user_metadata?.avatar_url ||\n                       user.user_metadata?.picture\n\n      console.log('Attempting to create user profile:', {\n        userId,\n        email: user.email,\n        displayName,\n        avatarUrl\n      })\n\n      // Create user profile with upsert to handle conflicts\n      const { data, error } = await supabase\n        .from('users')\n        .upsert({\n          id: userId,\n          email: user.email!,\n          display_name: displayName,\n          avatar_url: avatarUrl,\n          updated_at: new Date().toISOString()\n        }, {\n          onConflict: 'id'\n        })\n        .select()\n        .single()\n\n      if (error) {\n        console.error('Error creating user profile:', {\n          message: error.message,\n          details: error.details,\n          hint: error.hint,\n          code: error.code,\n          userId: userId,\n          email: user.email,\n          displayName: displayName\n        })\n\n        // If we still can't create the user, set a minimal user object\n        // so the app doesn't get stuck in a loop\n        if (error.message.includes('406') || error.code === 'PGRST301') {\n          console.log('Database access issue, using auth user data as fallback')\n          setUser({\n            id: userId,\n            email: user.email!,\n            display_name: displayName,\n            avatar_url: avatarUrl,\n            created_at: user.created_at,\n            updated_at: new Date().toISOString()\n          })\n        }\n        return\n      }\n\n      console.log('User profile created successfully:', data)\n      setUser(data)\n    } catch (error) {\n      console.error('Error creating user profile:', error)\n\n      // Fallback: use the auth user data to prevent infinite loops\n      if (supabaseUser) {\n        const displayName = supabaseUser.user_metadata?.display_name ||\n                           supabaseUser.user_metadata?.name ||\n                           supabaseUser.user_metadata?.full_name ||\n                           supabaseUser.email?.split('@')[0] ||\n                           'Anonymous'\n\n        setUser({\n          id: userId,\n          email: supabaseUser.email!,\n          display_name: displayName,\n          avatar_url: supabaseUser.user_metadata?.avatar_url || supabaseUser.user_metadata?.picture,\n          created_at: supabaseUser.created_at,\n          updated_at: new Date().toISOString()\n        })\n      }\n    }\n  }\n\n  const signInWithGoogle = async () => {\n    try {\n      // Check if Supabase is properly configured\n      if (!supabase) {\n        alert('Please configure Supabase environment variables. See README.md for setup instructions.')\n        return\n      }\n\n      const { error } = await supabase.auth.signInWithOAuth({\n        provider: 'google',\n        options: {\n          redirectTo: `${window.location.origin}/auth/callback`\n        }\n      })\n      if (error) throw error\n    } catch (error) {\n      console.error('Error signing in with Google:', error)\n      throw error\n    }\n  }\n\n  const signOut = async () => {\n    try {\n      if (!supabase) return\n\n      const { error } = await supabase.auth.signOut()\n      if (error) throw error\n      setUser(null)\n      setSupabaseUser(null)\n    } catch (error) {\n      console.error('Error signing out:', error)\n      throw error\n    }\n  }\n\n  const updateUserProfile = async (updates: Partial<Pick<User, 'display_name' | 'avatar_url'>>) => {\n    if (!supabase || !user) {\n      throw new Error('User not authenticated or Supabase not configured')\n    }\n\n    try {\n      const { data, error } = await supabase\n        .from('users')\n        .update({\n          ...updates,\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', user.id)\n        .select()\n        .single()\n\n      if (error) {\n        console.error('Error updating user profile:', error)\n        throw error\n      }\n\n      // Update the local user state\n      setUser(data)\n    } catch (error) {\n      console.error('Error updating user profile:', error)\n      throw error\n    }\n  }\n\n  const value = {\n    user,\n    supabaseUser,\n    loading,\n    signInWithGoogle,\n    signOut,\n    updateUserProfile\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AAeA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kCAAkC;QAClC,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;YACb,WAAW;YACX;QACF;QAEA,sBAAsB;QACtB,MAAM,oBAAoB;YACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAE,IAAI,CAAC,UAAU;YAC7D,IAAI,SAAS,MAAM;gBACjB,gBAAgB,QAAQ,IAAI;gBAC5B,IAAI;oBACF,MAAM,iBAAiB,QAAQ,IAAI,CAAC,EAAE;gBACxC,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,yDAAyD;oBACvE,mBAAmB,QAAQ,IAAI;gBACjC;YACF;YACA,WAAW;QACb;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAE,IAAI,CAAC,iBAAiB,CACjE,OAAO,OAAO;YACZ,QAAQ,GAAG,CAAC,sBAAsB,OAAO,SAAS,MAAM;YAExD,IAAI,SAAS,MAAM;gBACjB,gBAAgB,QAAQ,IAAI;gBAE5B,mDAAmD;gBACnD,IAAI;oBACF,MAAM,iBAAiB,QAAQ,IAAI,CAAC,EAAE;gBACxC,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,iDAAiD;oBAC/D,8CAA8C;oBAC9C,mBAAmB,QAAQ,IAAI;gBACjC;YACF,OAAO;gBACL,gBAAgB;gBAChB,QAAQ;YACV;YACA,WAAW;QACb;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,0CAA0C,SAAS,KAAK;QAEpE,MAAM,cAAc,SAAS,aAAa,EAAE,gBACzB,SAAS,aAAa,EAAE,QACxB,SAAS,aAAa,EAAE,aACxB,SAAS,aAAa,EAAE,cACxB,SAAS,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IAC7B;QAEnB,MAAM,eAAqB;YACzB,IAAI,SAAS,EAAE;YACf,OAAO,SAAS,KAAK;YACrB,cAAc;YACd,YAAY,SAAS,aAAa,EAAE,cAAc,SAAS,aAAa,EAAE;YAC1E,YAAY,SAAS,UAAU;YAC/B,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,QAAQ;QACR,QAAQ,GAAG,CAAC,0BAA0B;IACxC;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;QAEf,IAAI;YACF,4EAA4E;YAC5E,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YAC5D,IAAI,CAAC,YAAY,OAAO,EAAE;gBACxB,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,WAAW,GAAG,iEAAiE;;YAElF,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,gCAAgC;oBAC5C,SAAS,MAAM,OAAO;oBACtB,SAAS,MAAM,OAAO;oBACtB,MAAM,MAAM,IAAI;oBAChB,MAAM,MAAM,IAAI;oBAChB,QAAQ;gBACV;gBAEA,8BAA8B;gBAC9B,IAAI,MAAM,IAAI,KAAK,cAAc,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM;oBACvE,QAAQ,GAAG,CAAC;oBACZ,MAAM,kBAAkB;oBACxB;gBACF;gBAEA,6DAA6D;gBAC7D,0CAA0C;gBAC1C;YACF;YAEA,IAAI,MAAM;gBACR,QAAQ;YACV,OAAO;gBACL,mCAAmC;gBACnC,QAAQ,GAAG,CAAC;gBACZ,MAAM,kBAAkB;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,gEAAgE;YAChE,MAAM,kBAAkB;QAC1B;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,sHAAA,CAAA,WAAQ,IAAI,CAAC,cAAc;QAEhC,IAAI;YACF,mCAAmC;YACnC,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAExE,IAAI,WAAW;gBACb,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C;YACF;YAEA,MAAM,OAAO,SAAS,IAAI;YAC1B,IAAI,CAAC,MAAM;YAEX,qDAAqD;YACrD,MAAM,cAAc,KAAK,aAAa,EAAE,gBACrB,KAAK,aAAa,EAAE,QACpB,KAAK,aAAa,EAAE,aACpB,KAAK,aAAa,EAAE,cACpB,KAAK,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IACzB;YAEnB,MAAM,YAAY,KAAK,aAAa,EAAE,cACrB,KAAK,aAAa,EAAE;YAErC,QAAQ,GAAG,CAAC,sCAAsC;gBAChD;gBACA,OAAO,KAAK,KAAK;gBACjB;gBACA;YACF;YAEA,sDAAsD;YACtD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,IAAI;gBACJ,OAAO,KAAK,KAAK;gBACjB,cAAc;gBACd,YAAY;gBACZ,YAAY,IAAI,OAAO,WAAW;YACpC,GAAG;gBACD,YAAY;YACd,GACC,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,gCAAgC;oBAC5C,SAAS,MAAM,OAAO;oBACtB,SAAS,MAAM,OAAO;oBACtB,MAAM,MAAM,IAAI;oBAChB,MAAM,MAAM,IAAI;oBAChB,QAAQ;oBACR,OAAO,KAAK,KAAK;oBACjB,aAAa;gBACf;gBAEA,+DAA+D;gBAC/D,yCAAyC;gBACzC,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU,MAAM,IAAI,KAAK,YAAY;oBAC9D,QAAQ,GAAG,CAAC;oBACZ,QAAQ;wBACN,IAAI;wBACJ,OAAO,KAAK,KAAK;wBACjB,cAAc;wBACd,YAAY;wBACZ,YAAY,KAAK,UAAU;wBAC3B,YAAY,IAAI,OAAO,WAAW;oBACpC;gBACF;gBACA;YACF;YAEA,QAAQ,GAAG,CAAC,sCAAsC;YAClD,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAE9C,6DAA6D;YAC7D,IAAI,cAAc;gBAChB,MAAM,cAAc,aAAa,aAAa,EAAE,gBAC7B,aAAa,aAAa,EAAE,QAC5B,aAAa,aAAa,EAAE,aAC5B,aAAa,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IACjC;gBAEnB,QAAQ;oBACN,IAAI;oBACJ,OAAO,aAAa,KAAK;oBACzB,cAAc;oBACd,YAAY,aAAa,aAAa,EAAE,cAAc,aAAa,aAAa,EAAE;oBAClF,YAAY,aAAa,UAAU;oBACnC,YAAY,IAAI,OAAO,WAAW;gBACpC;YACF;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI;YACF,2CAA2C;YAC3C,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;gBACb,MAAM;gBACN;YACF;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,eAAe,CAAC;gBACpD,UAAU;gBACV,SAAS;oBACP,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC;gBACvD;YACF;YACA,IAAI,OAAO,MAAM;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,MAAM,UAAU;QACd,IAAI;YACF,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;YAEf,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC7C,IAAI,OAAO,MAAM;YACjB,QAAQ;YACR,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,sHAAA,CAAA,WAAQ,IAAI,CAAC,MAAM;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,GAAG,OAAO;gBACV,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,MAAM;YACR;YAEA,8BAA8B;YAC9B,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}]}